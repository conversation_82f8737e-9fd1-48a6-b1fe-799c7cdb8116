# -*- coding: utf-8-with-signature -*-
#
# Some helper functions to open, read, and modify excel workbooks

function Start-Excel
{
	$excelHndl = @{}

	# Start Excel
	$excelHndl["beforeExcel"] = Get-Date
	$excel = New-Object -COM "Excel.Application"
	$excel.Visible = $false
	$excel.DisplayAlerts = $false
	$Excel.Interactive = $false

	$excelHndl["excel"] = $excel
	$excelHndl["afterExcel"] = Get-Date

	Write-Lg "Started Excel"

	return $excelHndl
}

function Stop-Excel
{
	param (
        [Parameter(Mandatory=$true)]
        [hashtable] $excelHndl
    )

	# Properly close excel COM app
	$excel = $excelHndl["excel"]
	$excel.Workbooks | ForEach-Object {$_.Close()}
	$excel.Quit()

	# Execute garbage collection to ensure correct deallocation of com objects
	[System.GC]::Collect()
	[System.GC]::WaitForPendingFinalizers()

	$beforeExcel = $excelHndl["beforeExcel"]
	$afterExcel = $excelHndl["afterExcel"]

	# Kill the remaining ghost excel process (if it exists)
	Get-Process excel |
	  Where {$beforeExcel -le $_.StartTime -and $_.StartTime -le $afterExcel} | Stop-Process

	Write-Lg "Excel tidy up complete"
}


function Set-WorkbookAutoSaveOn
{
	param (
        [Parameter(Mandatory=$true)]
        [hashtable] $workbookHndl
    )

	$workbook = $workbookHndl["workbook"]

	for ($i=0; $i -lt 7; $i++){
		$delay = 100.0 * [math]::Pow(2,$i) / 1000.0

		if ($i -ne 0){
			Write-Warning "Sleeping $delay seconds waiting for synch..."
			Start-Sleep -Seconds $delay
		}

		if ($null -eq $workbook.AutoSaveOn){
			Write-Warning "No sharepoint auto-save available (yet)..."
		}
		else{
			try{
				$workbook.AutoSaveOn = $true
				Write-Lg "Activated auto-save"
				if ($workbook.AutoSaveOn){
					$workbook.Save()
					Start-Sleep -Seconds 1.0
					Write-Lg "Successful Sharepoint synchronization!"
					return $true
				}
			}
			catch{
				Write-Warning "Error activating auto-save..."
			}
		}
	}
	return $false
}

function Open-Workbook
{
	param (
        [Parameter(Mandatory=$true)]
        [hashtable] $excelHndl,
		[Parameter(Mandatory=$true)]
        [string] $workbookFilePath,
		[Parameter()]
        [bool] $isSharepoint
    )


	Write-Lg "Opening workbook $workbookFilePath"
	$excel = $excelHndl["excel"]

	$workbookHndl = @{}

	$workbook = $excel.Workbooks.Open($workbookFilePath)

	$workbook.Queries.FastCombine = $true
	$workbookHndl["workbook"] = $workbook
	$workbookHndl["filepath"] = $workbookFilePath
	$workbookHndl["sharepoint"] = $isSharepoint
	$workbookHndl["synchSuccess"] = $false

	if ($isSharepoint){
		$workbookHndl["synchSuccess"] = Set-WorkbookAutoSaveOn -workbookHndl $workbookHndl
	}

	$workbook.ConflictResolution = 2
	if($workbook.AutoSaveOn){
		$workbook.AutoSaveOn = $false
	}

	return $workbookHndl
}


function Close-Workbook
{
	param (
        [Parameter(Mandatory=$true)]
        [hashtable] $workbookHndl
    )

	try{

		$workbook = $workbookHndl["workbook"]

		$synchSuccess = $false
		if ($workbookHndl["sharepoint"]){
			$synchSuccess = Set-WorkbookAutoSaveOn -workbookHndl $workbookHndl
			$workbookHndl["synchSuccess"] = $synchSuccess -and $workbookHndl["synchSuccess"]
		}

		# If we did not activate auto-save, we need to save manually
		if (-not $synchSuccess){
			Write-Lg "Saving workbook manually..."
			$workbook.Save()
		}
		$workbook.Close()
		Write-Lg "Closed workbook $($workbookHndl['filepath'])"
	}
	catch{
		Write-Lg "An error occurred: $_" -Level "ERROR"
		Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
		Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
		throw $_
	}
}


function Sync-Workbook
{
	param (
        [Parameter(Mandatory=$true)]
        [hashtable] $excelHndl,
		[Parameter(Mandatory=$true)]
        [string] $workbookFilePath
    )

	for ($i=0; $i -lt 3; $i++){

		$workbookHndl = Open-Workbook -excelHndl $excelHndl -workbookFilePath $workbookFilePath -isSharepoint $true
		Close-Workbook -workbookHndl $workbookHndl
		if ($workbookHndl["synchSuccess"]){
			return $true
		}

		Start-Sleep -Seconds 1
	}
	return $false
}


# Read value from a named Excel table via a key, a key column and a value column
function Read-TableEntry
{
	param(
		[Parameter(Mandatory)] $Sheet, # Sheet containing table
		[Parameter(Mandatory)] $Tab, # Table name
		[Parameter(Mandatory)] $KeyCol, # Name of column which holds the key
		[Parameter(Mandatory)] $ValueCol, # Name of column which holds the value
		[Parameter(Mandatory)] $Key, # The key
		$Default # The default value in case the key cannot be
				 # found. If not given, an error is thrown if the key
				 # cannot be found
	)

	$KeyRange = $Sheet.Range("$($Tab)[$($KeyCol)]")
	$KeyCell = $KeyRange.Find($Key)
	if ($null -eq $KeyCell){
		if ($null -eq $Default){
			throw "Could not find tab entry $Key in column $KeyCol !"
		}
		else{
			return $Default
		}
	}
	$ValueRange = $Sheet.Range("$($Tab)[$($ValueCol)]")
	$ValueCell = $Sheet.Cells($KeyCell.Row, $ValueRange.Column)
	$Value = $ValueCell.Value2
	return $Value
}

function Write-TableEntry
{
	param(
		[Parameter(Mandatory)] $Sheet, # Sheet containing table
		[Parameter(Mandatory)] $Tab, # Table name
		[Parameter(Mandatory)] $KeyCol, # Name of column which holds the key
		[Parameter(Mandatory)] $ValueCol, # Name of column which holds the value
		[Parameter(Mandatory)] $Key, # The key
		[Parameter(Mandatory)] $Value # The value to be written

	)

	$KeyRange = $Sheet.Range("$($Tab)[$($KeyCol)]")
	$KeyCell = $KeyRange.Find($Key)
	if ($null -eq $KeyCell){
		if ($null -eq $Default){
			throw "Could not find tab entry $Key in column $KeyCol !"
		}
		else{
			return $Default
		}
	}
	$ValueRange = $Sheet.Range("$($Tab)[$($ValueCol)]")
	$Sheet.Cells($KeyCell.Row, $ValueRange.Column) = $Value
}

function Read-TableColumn
{
	param(
		[Parameter(Mandatory)] $Sheet, # Sheet containing table
		[Parameter(Mandatory)] $Tab, # Table name
		[Parameter(Mandatory)] $Col # Name of column  holds the key
	)

    $Count = $Sheet.Range($Tab).Rows.Count
	$Result = @()
	for ($i=1; $i -le $Count; $i++){
		$Result += $Sheet.Range("$($Tab)[$($Col)]").Cells($i,1).Value2
	}

	return $Result
}

