### Parameters
#
param (
	[Parameter()]
    [switch]$Silent,
	[string]$Timestamp
)

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$GlobalScriptParameters = Get-SetupParameters
$SharePointMasterPath = $GlobalScriptParameters.SharePointMasterPath
$SharePointMasterVsBudgetPath = $GlobalScriptParameters.SharePointMasterVsBudgetPath
$Year = $GlobalScriptParameters.Year

$LogFilePath  = (Join-Path -Path $PSScriptRoot -ChildPath "..\..\Logs\CheckMasterLog.csv")
$Writer = [System.IO.StreamWriter]::new($LogFilePath, $true, [System.Text.Encoding]::UTF8)
if ($null -eq $Timestamp){
	$Timestamp = $(Get-Date -Format "dd.MM.yyyy HH:mm:ss")
}

# Start Excel
$ExcelHndl = Start-Excel

function Write-CsvLine {
    param (
        [Parameter(Mandatory)]
        [object[]]$Values
    )

    # Escape and quote each value
    $escaped = $Values | ForEach-Object {
        $s = $_.ToString()

        # Escape double quotes by doubling them
        $s = $s -replace '"', '""'

        # Wrap in double quotes
        '"' + $s + '"'
    }

    # Join with commas
    $line = $escaped -join ','

    # Write to the existing stream
    $Writer.WriteLine($line)
}

try{

	$WorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $SharePointMasterPath -isSharepoint $true
	$workbook = $workbookHndl["workbook"]

	# Check Workload Master
	$sheet = $workbook.Sheets.item("Workload Master")
	$FnPrms = @{Sheet = $sheet; Tab = "WorkloadMaster"; Col = "Total/%"}
	$WorkloadCol = Read-TableColumn @FnPrms -NullValue 0.0
	$FnPrms = @{Sheet = $sheet; Tab = "WorkloadMaster"; Col = "Project"}
	$ProjectCol = Read-TableColumn @FnPrms

	$WorkloadSum = ($WorkloadCol | Measure-Object -Sum).Sum
	Write-Lg "Total Workload: $WorkloadSum"
	$ProjectWorkload = Group-AndSum -Keys $ProjectCol -Values $WorkloadCol
	$ProjectWorkloadHash = Get-ValueListHash -ValueList $ProjectWorkload.Values
	Write-Lg "Project Workload Hash: $ProjectWorkloadHash"
	$WorkloadSheetStatus = $sheet.Cells(2,2).Value2
	Write-Lg "Workload Sheet Status: $WorkloadSheetStatus"

	Write-CSVLine ($Timestamp, $Year, "Total Workload", "ALL", $WorkloadSheetStatus, $WorkloadSum)
	Write-CSVLine ($Timestamp, $Year, "Project Workload Hash", "ALL", $ProjectWorkloadHash, 0)
	$ProjectWorkload.Keys | ForEach-Object {
		Write-CSVLine ($Timestamp, $Year, "Project Workload", $_, " ", $ProjectWorkload[$_])
	}

	# Check Direct Cost Master
	$sheet = $workbook.Sheets.item("Direct Cost Master")
	$FnPrms = @{Sheet = $sheet; Tab = "DirectCostMaster"; Col = "Total / CHF"}
	$TotalCol = Read-TableColumn @FnPrms -NullValue 0.0
	$FnPrms = @{Sheet = $sheet; Tab = "DirectCostMaster"; Col = "Project"}
	$ProjectCol = Read-TableColumn @FnPrms

	$DirectCostSum = ($TotalCol | Measure-Object -Sum).Sum
	Write-Lg "Total Direct Cost: $DirectCostSum"
	$ProjectDirectCost = Group-AndSum -Keys $ProjectCol -Values $TotalCol
	$ProjectDirectCostHash = Get-ValueListHash -ValueList $ProjectDirectCost.Values
	Write-Lg "Project Total Direct Cost Hash: $ProjectDirectCostHash"
	$DirectCostSheetStatus = $sheet.Cells(2,2).Value2
	Write-Lg "Direct Cost Sheet Status: $DirectCostSheetStatus"

	Write-CSVLine ($Timestamp, $Year, "Total Direct Cost", "ALL", $DirectCostSheetStatus, $DirectCostSum)
	Write-CSVLine ($Timestamp, $Year, "Project Direct Cost Hash", "ALL", $ProjectDirectCostHash, 0)
	$ProjectDirectCost.Keys | ForEach-Object {
		Write-CSVLine ($Timestamp, $Year, "Project Direct Cost", $_, " ", $ProjectDirectCost[$_])
	}

	# Check OVERALL
	$OverallHash = Get-ValueListHash -ValueList ($ProjectWorkloadHash, $ProjectDirectCostHash) -Length 6
	Write-Lg "OVERALL Hash: $OverallHash"
	Write-CSVLine ($Timestamp, $Year, "OVERALL Hash", "ALL", $OverallHash, 0)

	$OverallStatus = "OK"
	if ($WorkloadSheetStatus -ne "OK" -or $DirectCostSheetStatus -ne "OK"){
		$OverallStatus = "DIRTY"
	}
	Write-Lg "OVERALL sheet status: $OverallStatus"
	Write-CSVLine ($Timestamp, $Year, "OVERALL Status", "ALL", $OverallStatus, 0)

	Close-Workbook -workbookHndl $workbookHndl

	# Check Master vs Budget
	$WorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $SharePointMasterVsBudgetPath -isSharepoint $true

	$sheet = $workbook.Sheets.item("Master Summary")
	$FnPrms = @{Sheet = $sheet; Tab = "GetMasterSummary"; Col = "TOTAL woC"}
	$WorkloadCol = Read-TableColumn @FnPrms -NullValue 0.0
	$FnPrms = @{Sheet = $sheet; Tab = "GetMasterSummary"; Col = "Project"}
	$ProjectCol = Read-TableColumn @FnPrms

}
catch{
	Write-Lg "An error occurred: $_" -Level "ERROR"
	Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
	Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
}
finally{
	$Writer.Close()
	Stop-Excel -excelHndl $ExcelHndl
}

if (-not $Silent.IsPresent)
{
	Pause
}
