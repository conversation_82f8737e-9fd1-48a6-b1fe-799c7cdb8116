### Parameters
#
param (
	[Parameter()]
    [switch]$prmSilent
)

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$GlobalScriptParameters = Get-SetupParameters
$SharePointMasterPath = $GlobalScriptParameters.SharePointMasterPath

# Start Excel
$ExcelHndl = Start-Excel

try{

	$WorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $SharePointMasterPath -isSharepoint $true

	$workbook = $workbookHndl["workbook"]
	$sheet = $workbook.Sheets.item("Workload Master")
	$FnPrms = @{Sheet = $sheet; Tab = "WorkloadMaster"; Col = "Total/%"}
	$WorkloadCol = Read-TableColumn @FnPrms
	$FnPrms = @{Sheet = $sheet; Tab = "WorkloadMaster"; Col = "Project"}
	$ProjectCol = Read-TableColumn @FnPrms

	$WorkloadSum = ($WorkloadCol | Measure-Object -Sum).Sum
	Write-Lg "Total Workload: $WorkloadSum"

	$ProjectWorkload = [ordered]@{};
	$ProjectCol.Zip($WorkloadCol) | ForEach-Object { $ProjectWorkload[$_.Item1] = $_.Item2 }
	


}
catch{
	Write-Lg "An error occurred: $_" -Level "ERROR"
	Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
	Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
}
finally{
	Stop-Excel -excelHndl $ExcelHndl
}

if (-not $prmSilent.IsPresent)
{
	Pause
}
