### Parameters
#
param (
	[Parameter()]
    [switch]$Silent,
	[string]$Timestamp
)

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$GlobalScriptParameters = Get-SetupParameters
$SharePointMasterPath = $GlobalScriptParameters.SharePointMasterPath
$SharePointMasterVsBudgetPath = $GlobalScriptParameters.SharePointMasterVsBudgetPath
$Year = $GlobalScriptParameters.Year

$LogFilePath  = (Join-Path -Path $PSScriptRoot -ChildPath "..\..\Logs\CheckMasterLog.csv")
$Writer = [System.IO.StreamWriter]::new($LogFilePath, $true, [System.Text.Encoding]::UTF8)
if ($null -eq $Timestamp){
	$Timestamp = $(Get-Date -Format "dd.MM.yyyy HH:mm:ss")
}

# Start Excel
$ExcelHndl = Start-Excel

function Write-CsvLine {
    param (
        [Parameter(Mandatory)]
        [object[]]$Values
    )

    # Escape and quote each value
    $escaped = $Values | ForEach-Object {
        $s = $_.ToString()

        # Escape double quotes by doubling them
        $s = $s -replace '"', '""'

        # Wrap in double quotes
        '"' + $s + '"'
    }

    # Join with commas
    $line = $escaped -join ','

    # Write to the existing stream
    $Writer.WriteLine($line)
}

function Invoke-SheetData {
	param(
		[Parameter(Mandatory)]$Sheet,
		[Parameter(Mandatory)]$ProjectCol,
		[Parameter(Mandatory)]$ValueCol,
		[Parameter(Mandatory)]$MetricName
	)

	$ValueSum = ($ValueCol | Measure-Object -Sum).Sum
	Write-Lg "Total ${MetricName}: $ValueSum"
	$ProjectValues = Group-AndSum -Keys $ProjectCol -Values $ValueCol
	$ProjectValuesHash = Get-ValueListHash -ValueList $ProjectValues.Values
	Write-Lg "Project ${MetricName} Hash: $ProjectValuesHash"
	$SheetStatus = $Sheet.Cells(2,2).Value2
	Write-Lg "${MetricName} Sheet Status: $SheetStatus"

	Write-CsvLine ($Timestamp, $Year, "Total ${MetricName}", "ALL", $SheetStatus, $ValueSum)
	Write-CsvLine ($Timestamp, $Year, "Project ${MetricName} Hash", "ALL", $ProjectValuesHash, 0)
	$ProjectValues.Keys | ForEach-Object {
		Write-CsvLine ($Timestamp, $Year, "Project ${MetricName}", $_, " ", $ProjectValues[$_])
	}

	# Return the hash for use in overall calculations
	return $ProjectValuesHash
}

try{

	$WorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $SharePointMasterPath -isSharepoint $true
	$workbook = $workbookHndl["workbook"]

	# Check Workload Master
	$sheet = $workbook.Sheets.item("Workload Master")
	$FnPrms = @{Sheet = $sheet; Tab = "WorkloadMaster"; Col = "Total/%"}
	$WorkloadCol = Read-TableColumn @FnPrms -NullValue 0.0
	$FnPrms = @{Sheet = $sheet; Tab = "WorkloadMaster"; Col = "Project"}
	$ProjectCol = Read-TableColumn @FnPrms

	$ProjectWorkloadHash = Invoke-SheetData -Sheet $sheet -ProjectCol $ProjectCol -ValueCol $WorkloadCol -MetricName "Workload"

	# Check Direct Cost Master
	$sheet = $workbook.Sheets.item("Direct Cost Master")
	$FnPrms = @{Sheet = $sheet; Tab = "DirectCostMaster"; Col = "Total / CHF"}
	$TotalCol = Read-TableColumn @FnPrms -NullValue 0.0
	$FnPrms = @{Sheet = $sheet; Tab = "DirectCostMaster"; Col = "Project"}
	$ProjectCol = Read-TableColumn @FnPrms

	$ProjectDirectCostHash = Invoke-SheetData -Sheet $sheet -ProjectCol $ProjectCol -ValueCol $TotalCol -MetricName "Direct Cost" -Timestamp $Timestamp -Year $Year

	# Check OVERALL
	$OverallHash = Get-ValueListHash -ValueList ($ProjectWorkloadHash, $ProjectDirectCostHash) -Length 6
	Write-Lg "OVERALL Hash: $OverallHash"
	Write-CSVLine ($Timestamp, $Year, "OVERALL Hash", "ALL", $OverallHash, 0)

	$OverallStatus = "OK"
	if ($WorkloadSheetStatus -ne "OK" -or $DirectCostSheetStatus -ne "OK"){
		$OverallStatus = "DIRTY"
	}
	Write-Lg "OVERALL sheet status: $OverallStatus"
	Write-CSVLine ($Timestamp, $Year, "OVERALL Status", "ALL", $OverallStatus, 0)

	Close-Workbook -workbookHndl $workbookHndl

	# Check Master vs Budget
	#$WorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $SharePointMasterVsBudgetPath -isSharepoint $true
	#$workbook = $WorkbookHndl["workbook"]

	#$sheet = $workbook.Sheets.item("Master Summary")
	#$FnPrms = @{Sheet = $sheet; Tab = "GetMasterSummary"; Col = "TOTAL woC"}
	#$TOTALCol = Read-TableColumn @FnPrms -NullValue 0.0
	#$FnPrms = @{Sheet = $sheet; Tab = "GetMasterSummary"; Col = "Project"}
	#$ProjectCol = Read-TableColumn @FnPrms
}
catch{
	Write-Lg "An error occurred: $_" -Level "ERROR"
	Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
	Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
}
finally{
	$Writer.Close()
	Stop-Excel -excelHndl $ExcelHndl
}

if (-not $Silent.IsPresent)
{
	Pause
}
