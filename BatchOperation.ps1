param (
    [switch]$prmSilent,
    [string[]]$prmYears,
    [string[]]$prmOperations,
    [string[]]$prmUpdates
)

$InformationPreference = 'Continue'

Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force

Write-Lg "Params $prmSilent $prmYears $prmOperations $prmUpdates $($PSBoundParameters.Keys)"

$LogFilePath  = (Join-Path -Path $PSScriptRoot -ChildPath "..\..\Logs\Run_$(Get-Date -Format yyyyMMdd_HHmmss).txt")
Start-Transcript -Path $LogFilePath

$StartPath = Get-Location
$Years = @("2023","2024", "2025", "2026", "2027", "2028", "2029")

$Operations = [ordered]@{}
$Operations["1 Create RFCs"] = @{"Script" = "CreateRFCs.ps1"; "Pattern" = "Silent"}
$Operations["2 Create Master"] = @{"Script" = "CreateMaster.ps1"; "Pattern" = "Silent"}
$Operations["3 Update"] = @{"Script" = "UpdateRFCs.ps1"; "Pattern" = "Updates"}
$Operations["4 Check Master"] = @{"Script" = "CheckMaster.ps1"; "Pattern" = "Timestamp"}

try{
	$StartTime = Start-Timer
	$SelectedYears = $prmYears
	if ($null -eq $prmYears){
		$SelectedYears = Select-ByGUI -CandidateList $Years
	}
	Write-Lg "Selected Years: $SelectedYears"

	$SelectedOperations = $prmOperations
	if($null -eq $prmOperations){
		$SelectedOperations = Select-ByGUI -CandidateList $Operations.Keys
	}
	Write-Lg "Selected Operations: $SelectedOperations"

	# Only query user for update types if the update operation is selected
	if("3 Update" -in $SelectedOperations){
		$SelectedUpdates = $prmUpdates
		if($null -eq $prmUpdates){
			$AllUpdateModes = @("ERP", "MASTER", "EVAL", "LEADS")
			$SelectedUpdates = Select-ByGUIWithOptions  -CandidateList $AllUpdateModes -OptionsPrm @{"SelectionMode"="MultiExtended"}
		}
		Write-Lg "Selected Updates: $SelectedUpdates"
	}

	$Timestamp = $(Get-Date -Format "dd.MM.yyyy HH:mm:ss")

	foreach ($Year in $SelectedYears){
		Write-Lg "Processing year $($Year):"
		$YearPath = Join-Path -Path $StartPath -ChildPath "$Year\Master"
		Write-Lg "Switching to directory $YearPath"
		cd $YearPath

		foreach($OpKey in $SelectedOperations){
			Write-Lg "Executing operation $($OpKey):"
			$ScriptPath = Join-Path -Path $PSScriptRoot -ChildPath $Operations[$OpKey]["Script"]
			switch($Operations[$OpKey]["Pattern"]){
				"Silent" { & $ScriptPath -Silent }
				"Updates" { & $ScriptPath -prmSilent -prmUpdates $SelectedUpdates }
				"Timestamp" { & $ScriptPath -Silent -Timestamp $Timestamp }
			}
		}
	}
	Stop-Timer -StartTime $StartTime -OperationName "OVERALL"
}
catch{
	Write-Lg "An error occurred: $_" -Level "ERROR"
	Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
	Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
}
finally{
	cd $StartPath
	Stop-Transcript
	if (-not $prmSilent){
		Pause
	}
}
