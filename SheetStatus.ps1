﻿### Parameters

# Line number in which the RFC workbook sheet "Properties" holds the Budget Master filepath
$RFCFilesPaths = @{
	"2023"=".\2023\Projects\";
	"2024"=".\2024\Projects\";
}

function Reset-Globals
{
	$global:GlobalWorkload = 0
	$global:GlobalCost = 0
	$global:Errors = @{}
}

function Add-Global-Error
{
	param($Label, $Value, $Owner)

	if($Value -eq "OK"){return}
	if($Owner -notin $global:Errors.keys){
		$global:Errors[$Owner] = @()
	}

	$global:Errors[$Owner] += $Label + " " + $Value
}

function Check-Workbook([ref]$ExcelPrm, $RFCFilePathPrm)
{
	try{
		$Filename = Split-Path $RFCFilePathPrm -leaf
		Write-Host "Opening workbook $Filename"
		$Workbook = $ExcelPrm.Value.Workbooks.Open($RFCFilePathPrm)
		$Workbook.AutoSaveOn = $false
	    $Workbook.Queries.FastCombine = $true
		$PropertySheet = $Workbook.Sheets.item("Properties")
		$MasterPropsTab = $PropertySheet.ListObjects["MasterProps"].Range
		$Owner = $MasterPropsTab.Find("Owner").Cells(1,2).Value2
		$WorkloadStatus = $Workbook.Sheets.item("Workload Plan").Cells(2,2).Value2
		$DirectCostStatus = $Workbook.Sheets.item("Direct Cost Plan").Cells(2,2).Value2
		$LogStatus = $Workbook.Sheets.item("Log").Cells(2,2).Value2
		Write-Host "`t`t`t $Owner`n`t`t`t W: $WorkloadStatus`n`t`t`t C: $DirectCostStatus`n`t`t`t L: $LogStatus"
		Add-Global-Error -Label "W:" -Value $WorkloadStatus -Owner $Owner
		Add-Global-Error -Label "C:" -Value $DirectCostStatus -Owner $Owner
		Add-Global-Error -Label "L:" -Value $LogStatus -Owner $Owner

		$ChangeDate = (Get-Item $RFCFilePathPrm).LastWriteTime
		$DiffDate = New-TimeSpan -Start $ChangeDate -End $(Get-Date)
		$DiffMinutes = $DiffDate.TotalMinutes
		Write-Host "`t`t`t Changed: $($DiffDate.TotalMinutes)"

		$WorkloadTotalRows = $Workbook.Sheets.item("Workload Plan").Range("WorkloadPlan[TOTAL/%]").Rows
		$WorkloadSum = $WorkloadTotalRows | ForEach-Object {$total=0} {$total += $_.Cells(1,1).Value2} {$total}
		Write-Host "`t`t`t Total Workload: $($WorkloadSum * 100)%"

		$CostTotalRows = $Workbook.Sheets.item("Direct Cost Plan").Range("DirectCostPlan[TOTAL]").Rows
		$CostSum = $CostTotalRows | ForEach-Object {$total=0} {$total += $_.Cells(1,1).Value2} {$total}
		Write-Host "`t`t`t Total Cost: $CostSum"

		$global:GlobalWorkload += $WorkloadSum
		$global:GlobalCost += $CostSum
	}
	finally{
		if ($Workbook -ne $null){ $Workbook.Close() }
	}
}



# Start Excel
$BeforeExcel = Get-Date
$Excel = New-Object -COM "Excel.Application"
$AfterExcel = Get-Date

$Report = ""

try{
	foreach($RFCFilesPath in $RFCFilesPaths.values)
	{
		Reset-Globals

		# Compute full file paths
		$RFCFilesPathAbs = (Resolve-Path -Path $RFCFilesPath).Path
		
		$Excel.Visible = $false
		$Excel.DisplayAlerts = $false

		$RFCFiles = Get-ChildItem -Path (Join-Path $RFCFilesPathAbs "*") -Recurse -Include "*.xlsx"

		Write-Host "`nCheck files in $RFCFilesPath"
		foreach($RFCFile in $RFCFiles){
			Check-Workbook -ExcelPrm ([ref]$Excel) -RFCFilePathPrm $RFCFile
		}

		$Report += "Report for path $RFCFilesPath`n"
		$Report += "Global Workload: $global:GlobalWorkload `tGlobal Cost: $global:GlobalCost`n"
		$Report += "Global Errors:`n"
		$global:Errors.keys | ForEach-Object {} {
			$Report += "`t$_ :`n"
			$global:Errors[$_] | ForEach-Object {} {$Report += "`t`t$_`n"}
		}
	}
	Write-Host $Report
}
finally
{
	Write-Host "Tidy Up Started"

	# Properly close excel COM app
	Write-Host "Found $($Excel.Workbooks.Count) open workbooks"
	$Excel.Workbooks | ForEach-Object {$_.Close()}
	$Excel.Quit()

	# Execute garbage collection to ensure correct deallocation of com objects
	[System.GC]::Collect()
	[System.GC]::WaitForPendingFinalizers()

	# Kill the remaining ghost excel process (if it exists)
	Get-Process | Where-Object{
        $_.ProcessName -eq "excel" -and $BeforeExcel -le $_.StartTime -and $_.StartTime -le $AfterExcel} | Stop-Process
	
	Write-Host "Tidy Up Complete"
}

Pause

