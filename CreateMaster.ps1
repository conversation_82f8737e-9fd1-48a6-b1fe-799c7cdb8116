# -*- coding: utf-8-with-signature -*-
## Description
#
#  Creates (usually overwrites) a number of files in the current
#  folder which is supposed to be the master folder. Essentially it
#  copies a template version of each file to the current folder then
#  sets the Setup Path in a given table entry of the file, then
#  updates that table's query follow and then updates all other
#  queries. In the end it renames the template file to the final file
#  name.
#
#  Regarding the Setup Path, it is currently hard coded that the path
#  is set in the tab "Parameters" and that the table cols are
#  "Property" and "Value" and that the key is "SetupPath".
#
## If the Silent flag is given, then all update types are processed without user selection
param(
    [switch]$Silent
)

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$tempFlag = "__TEMPMASTER__"


function Update-Target
{
	param (
		[Parameter(Mandatory=$true)]
		[hashtable] $excelHndl,
		[Parameter(Mandatory=$true)]
		[hashtable] $updateTarget,
		[Parameter(Mandatory=$true)]
		[int] $year
	)
	try{

		# Create temp folder
		$TempPath = (New-TemporaryDirectory).FullName

		# Determine target paths of copy and final file
		$templatePath = $updateTarget["Template"]
		$targetPath = $updateTarget["Target"] -replace "YYYY", "$($year)"
		$targetCopyPath = $targetPath -replace "\.xlsx", "$($tempFlag).xlsx"
		$targetCopyPath = Join-Path $TempPath (Split-Path $targetCopyPath -Leaf)
		$targetPathTab = $updateTarget["Tab"]

		Write-Verbose "Copy master template to $targetCopyPath"
		Copy-Item -Path $templatePath -Destination $targetCopyPath -Force

		# Open workbook
		$workbookHndl = Open-Workbook -excelHndl $excelHndl `
		  -workbookFilePath $targetCopyPath
		$workbook = $workbookHndl["workbook"]

		# Write the correct setup path
		$sheet = $workbook.Sheets.item("Parameters")
		$FnPrms = @{Sheet = $sheet; Tab = $targetPathTab; KeyCol = $updateTarget["KeyCol"];
					ValueCol = "Value";	Key="SetupPath"}
		Write-TableEntry @FnPrms -Value $setupSharePointPath

		# First Update the table with the file paths
		Update-MatchingQueries -WorkbookPrm $workbook -MatchStrPrm $targetPathTab `
		  -ShouldMatchPrm $true
		# Afterwards, update all other queries
		Update-AllQueries -WorkbookPrm $workbook -ExceptionListPrm @($targetPathTab)
		Wait-OnAllQueries -WorkbookPrm $workbook
		Close-Workbook -workbookHndl $workbookHndl

		# Wait a second to avoid collision of Workbook save and move
		Start-Sleep -Seconds 1.0

		# Now that the operation was a success, overwrite the old file
		# with the temp file
		Move-Item -Path $targetCopyPath -Destination $targetPath -Force
		Remove-Item -Recurse -Force $TempPath
		Write-Lg "Successfully moved updated file to $targetPath"

		$synchSuccess = Sync-Workbook -excelHndl $excelHndl -workbookFilePath $targetPath
		if (-not $synchSuccess){
			Write-Lg "Failed to synchronize workbook $targetPath with Sharepoint!" -Level "ERROR"
		}
		else{
			Write-Lg "Successfully verified Sharepoint synchronization"
		}
	}
	catch{
		Write-Warning "An error occurred: $_"
		Write-Warning "File: $($_.InvocationInfo.ScriptName)"
		Write-Warning "Line: $($_.InvocationInfo.ScriptLineNumber)"
		throw $_
	}
}


try{

	$rootFolder = Join-Path $PSScriptRoot "..\"
	$rootFolder = (Resolve-Path -Path $rootFolder).Path
	$yearFolder = Join-Path $rootFolder "YYYY"
	$masterFolder = Join-Path $yearFolder "Master"

	$prmUpdatesAll = @("MASTER", "MASTERVSBUDGET", "TEAMVIEW")
	if ($Silent) {
		# If Silent flag is set, select all update types
		$SelectedUpdates = $prmUpdatesAll
		Write-Lg "Silent mode: Selected all $($prmUpdatesAll.Count) update types: $($prmUpdatesAll -join ', ')"
	} else {
		# Otherwise, show GUI for user selection
		$SelectedUpdates = Select-ByGUIWithOptions -CandidateList $prmUpdatesAll -OptionsPrm @{"SelectionMode"="MultiExtended"}
	}

	$updateTargets = @()

	if ("MASTER" -in $SelectedUpdates){
		# Parameters of the master template file
		$templateFilename = "HSH_R&D_Master_YYYY.xlsx"
		$templatePath = Join-Path $PSScriptRoot $templateFilename
		$finalPath = Join-Path $masterFolder $templateFilename
		$updateTargets += @{Template = $templatePath; Target = $finalPath;
							Tab = "FilePaths"; KeyCol = "Property"}
	}

	# Parameters of the master vs budget template file
	if ("MASTERVSBUDGET" -in $SelectedUpdates){
		$templateFilename = "HSH_R&D_MasterVsBudget_YYYY.xlsx"
		$templatePath = Join-Path $PSScriptRoot $templateFilename
		$finalPath = Join-Path $masterFolder $templateFilename
		$updateTargets += @{Template = $templatePath; Target = $finalPath;
							Tab = "PropsTab"; KeyCol = "Property"}
	}

	# Parameters of team view file
	if ("TEAMVIEW" -in $SelectedUpdates){
		$templateFilename = "Master_YYYY_TeamView.xlsx"
		$templatePath = Join-Path $PSScriptRoot $templateFilename
		$finalPath = Join-Path $yearFolder $templateFilename
		$updateTargets += @{Template = $templatePath; Target = $finalPath;
							Tab = "PropsTab"; KeyCol = "Property"}
	}

	$setupParameters = Get-SetupParameters
	$setupSharePointPath = $setupParameters["SharePointSetupFilePath"]
	$year = $setupParameters["Year"]

	# Start Excel
	$excelHndl = Start-Excel

	foreach($target in $updateTargets)
	{
		$targetName = Split-Path $target["Target"] -Leaf
		$timerStart = Start-Timer
		Update-Target -excelHndl $excelHndl -updateTarget $target -year $year
		Stop-Timer -StartTime $timerStart -OperationName $targetName
	}
}
catch{
	Write-Lg "An error occurred: $_" -Level "ERROR"
	Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
	Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
}
finally	{
	if ($null -ne $excelHndl){
		Stop-Excel -excelHndl $excelHndl
	}

	if (-not $Silent) {
		Pause
	}
}


