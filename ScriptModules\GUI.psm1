
# Given a list of strings, show a GUI to select one or multiple or all of them.
# Returns the list of selected strings
# The $OptionsPrm is a HashMap which allows the following setting:
# "SelectionMode": Either "MultiExtended" or "One" (default)
# "Sorted": Either $true or $false (default)
# "Preselected": The string value of the item which should be selected initially
function Select-ByGUIWithOptions
{
	param($CandidateList, $OptionsPrm)

	if ($CandidateList -eq $null -or $CandidateList.Count -eq 0){ return @() }

	[void] [System.Reflection.Assembly]::LoadWithPartialName("System.Drawing")
	[void] [System.Reflection.Assembly]::LoadWithPartialName("System.Windows.Forms")
	$objForm = New-Object System.Windows.Forms.Form
	$objForm.Size = New-Object System.Drawing.Size(800,500)
	$objForm.StartPosition = 'CenterScreen'

	$objListbox = New-Object System.Windows.Forms.Listbox
	$objListbox.Location = New-Object System.Drawing.Size(10,10)
	$objListbox.Size = New-Object System.Drawing.Size(780,400)
	if ("SelectionMode" -in $OptionsPrm.Keys){
		$objListbox.SelectionMode = $OptionsPrm["SelectionMode"]
	}
	if ("Sorted" -in $OptionsPrm.Keys){
		$objListbox.sorted = $OptionsPrm["Sorted"]
	}
	$CandidateList | ForEach-Object{
		[void] $objListbox.Items.Add($_)
	}
	if ("Preselected" -in $OptionsPrm.Keys){
		$matchIndex = $objListbox.FindStringExact($OptionsPrm["Preselected"])
		if ($matchIndex -ne [System.Windows.Forms.ListBox]::NoMatches){
			$objListbox.SetSelected($matchIndex,$true)
		}
		else{
			throw "Could not find key to preselect in Listbox!"
		}
	}
	$objForm.Controls.Add($objListbox)

	$hasMultiSelect = if ( $objListbox.SelectionMode -in @("MultiExtended","MultiSimple") )
	{1} else {0}

	$OKButton = New-Object System.Windows.Forms.Button
	$OKButton.Location = New-Object System.Drawing.Point(10,410)
	$OKButton.Size = New-Object System.Drawing.Size(360,40)
	$OKButton.Text = 'Select'
	$OKButton.DialogResult = [System.Windows.Forms.DialogResult]::OK
	$objForm.AcceptButton = $OKButton
	$objForm.Controls.Add($OKButton)

	if ($hasMultiSelect){
		$AllButton = New-Object System.Windows.Forms.Button
		$AllButton.Location = New-Object System.Drawing.Point(410,410)
		$AllButton.Size = New-Object System.Drawing.Size(360,40)
		$AllButton.Text = 'Select All'
		$AllButton.DialogResult = [System.Windows.Forms.DialogResult]::Yes
		$objForm.Controls.Add($AllButton)
	}

	$Result = $objForm.ShowDialog()

	if ($Result -eq [System.Windows.Forms.DialogResult]::OK){
		return @($objListbox.SelectedItem,$objListbox.SelectedItems)[$hasMultiSelect]
	}
	elseif ($Result -eq [System.Windows.Forms.DialogResult]::Yes){
		return  $CandidateList
	}
	else { return @($null, @())[$hasMultiSelect] }
}

# Special version of Select-ByGUIWithOptions which uses MultiExtended selection mode
# and a sorted display
function Select-ByGUI
{
	param($CandidateList)
	return Select-ByGUIWithOptions -CandidateList $CandidateList -OptionsPrm @{"SelectionMode" = "MultiExtended"; "Sorted" = $true}
}

function Select-ByGUIWithLabels
{
	param($CandidateList, $CandidateLabels)

	if ($CandidateList.Count -ne $CandidateLabels.Count){
		throw "Size of candidates $($CandidateList.Count) and labels $($CandidateLabels.Count)does not match"
	}

	$LabelMap = @{}
	$i=0
	foreach($item in $CandidateList){
		$LabelMap.add($CandidateLabels[$i], $item)
		$i++
	}

	$SelectedLabels = Select-ByGUI -CandidateList $LabelMap.keys
	if ($SelectedLabels -eq $null -or $SelectedLabels.Count -eq 0){
		return @()
	}

	Write-Verbose "Selected Labels $SelectedLabels"
	return $LabelMap[$SelectedLabels]
}

# Opens a GUI to select among the given paths showing only the filenames and returns
# the list of the selected paths
function Select-FilesByGUI
{
	param($CandidateFiles)

	if ($CandidateFiles -eq $null -or $CandidateFiles.Count -eq 0){ return @() }

	$CandFilenames = @{}
	$CandidateFiles | ForEach-Object { $CandFilenames.add((Split-Path $_ -leaf), $_) }

	$SelectedFiles = Select-ByGUI -CandidateList $CandFilenames.keys

	return $CandFilenames[$SelectedFiles]
}

# Creates a GUI with one button per candidate. Returns the selected candidate when a button is clicked.
function Select-ByGUIWithButtons
{
	param($CandidateList)

	if ($null -eq $CandidateList -or $CandidateList.Count -eq 0){ return $null }

	[void] [System.Reflection.Assembly]::LoadWithPartialName("System.Drawing")
	[void] [System.Reflection.Assembly]::LoadWithPartialName("System.Windows.Forms")

	$objForm = New-Object System.Windows.Forms.Form
	$objForm.Text = "Select Option"
	$objForm.StartPosition = 'CenterScreen'
	$objForm.FormBorderStyle = 'FixedDialog'
	$objForm.MaximizeBox = $false
	$objForm.MinimizeBox = $false

	# Calculate form dimensions based on number of candidates
	$buttonHeight = 40
	$buttonWidth = 300
	$margin = 10
	$buttonsPerRow = 2
	$rows = [Math]::Ceiling($CandidateList.Count / $buttonsPerRow)

	$formWidth = ($buttonsPerRow * $buttonWidth) + (($buttonsPerRow + 1) * $margin)
	$formHeight = ($rows * $buttonHeight) + (($rows + 1) * $margin) + 40

	$objForm.Size = New-Object System.Drawing.Size($formWidth, $formHeight)

	# Create buttons for each candidate
	for ($i = 0; $i -lt $CandidateList.Count; $i++) {
		$candidate = $CandidateList[$i]
		$button = New-Object System.Windows.Forms.Button

		# Calculate button position
		$row = [Math]::Floor($i / $buttonsPerRow)
		$col = $i % $buttonsPerRow

		$x = $margin + ($col * ($buttonWidth + $margin))
		$y = $margin + ($row * ($buttonHeight + $margin))

		$button.Location = New-Object System.Drawing.Point($x, $y)
		$button.Size = New-Object System.Drawing.Size($buttonWidth, $buttonHeight)
		$button.Text = $candidate
		$button.Tag = $candidate

		# Add click event handler
		$button.Add_Click({
			$objForm.Tag = $this.Tag
			$objForm.DialogResult = [System.Windows.Forms.DialogResult]::OK
			$objForm.Close()
		})

		$objForm.Controls.Add($button)
	}

	# Show the form and return the selected candidate
	$result = $objForm.ShowDialog()

	if ($result -eq [System.Windows.Forms.DialogResult]::OK) {
		return $objForm.Tag
	} else {
		return $null
	}
}
