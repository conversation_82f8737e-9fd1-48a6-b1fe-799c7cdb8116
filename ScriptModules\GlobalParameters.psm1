# Read global parameters from setup excel file

#$VerbosePreference = "Continue" # Ensures verbose messages are displayed and logged

$setupFilePrefix = "HSH_R&D_Setup"

Import-Module -Name (Join-Path $PSScriptRoot "ExcelCom.psm1") -Force

# Returns the local path of the setup file which is assumed to be
# located in the current folder and starts with the pattern given in $setupFilePrefix
function Get-SetupFilePath {

    # Get the directory of the current script
    $currentFolder = ((Resolve-Path -Path ".").Path)

    # Search for files in the same folder that match the prefix
    $matchingFiles = Get-ChildItem -Path $currentFolder -Filter "$setupFilePrefix*"

    # Check the number of matches
    if ($matchingFiles.Count -eq 1) {
        $absoluteFilePath = $matchingFiles.FullName
        return $absoluteFilePath
    } elseif ($matchingFiles.Count -eq 0) {
        throw "No files found with the prefix '$FilePrefix' in the folder '$currentFolder'."
    } else {
        throw "Multiple files found with the prefix '$FilePrefix' in the folder '$currentFolder'. Please refine your search."
    }
}

function Get-SetupParameters
{
	$excelHndl = Start-Excel
	$setupFilePath = Get-SetupFilePath
	Write-Lg "Reading setup parameters from $setupFilePath"
	$workbookHndl = Open-Workbook -excelHndl $excelHndl -workbookFilePath $setupFilePath
	$sheet = $workbookHndl["workbook"].Sheets.item("General Properties")
	$tablePrms = @{Sheet=$sheet; Tab="PropsSetupTab"; KeyCol="Property"; ValueCol="Value"}

	$parameters = @{
		# Destination path of created files (may be a relative path)
		RelativeRFCFilesPath = "..\Projects\";
		# Filename of RFC file (should be in script folder)
		RFCfilename = "RFC_XXX_YYYY.xlsx";
		# Employees roster file
		RelativeRosterPath = "..\..\..\R&D_Employees_Global.xlsx";
	}

	$parameters['Year'] = [int](Read-TableEntry @tablePrms -Key "Year")
	$masterPath = Read-TableEntry @tablePrms -Key "MasterPath"
	$parameters['MasterFilename'] = Split-Path $masterPath -Leaf
	$budgetPath = Read-TableEntry @tablePrms -Key "BudgetPath"
	$parameters['BudgetFilename'] = Split-Path $budgetPath -Leaf
	$masterVsBudgetPath = Read-TableEntry @tablePrms -Key "MasterVsBudgetPath"
	$parameters['MasterVsBudgetFilename'] = Split-Path $masterVsBudgetPath -Leaf
	$parameters['SharePointMasterPath'] = $masterPath
	$parameters['SharePointMasterFolder'] = $masterPath -replace "/[^/]+$", "/"
	$parameters['SharePointSetupFilePath'] = Read-TableEntry @tablePrms -Key "SetupPath"
	$parameters['SharePointDomain'] = Read-TableEntry @tablePrms -Key "SharepointDomain"
	$parameters['SharePointRFCsPath'] = Read-TableEntry @tablePrms -Key "ProjectsPath"
	$parameters['SharePointRosterPath'] = Read-TableEntry @tablePrms -Key "EmployeeRoster"
	$parameters['SharePointMasterTeamView'] = Read-TableEntry @tablePrms -Key "TeamView"
	$parameters['SharePointRosterView'] = Read-TableEntry @tablePrms -Key "PublicRosterView"
	$parameters['SharePointDirectCost'] = Read-TableEntry @tablePrms -Key "DirectCostPath"
	$parameters['SharePointWorkload'] = Read-TableEntry @tablePrms -Key "WorkloadPath"

	Close-Workbook -workbookHndl $workbookHndl
	Stop-Excel -excelHndl $excelHndl
	Write-Lg "Done"

	return $parameters
}


