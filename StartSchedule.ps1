
$InformationPreference = 'Continue'
Import-Module -Name (Join-<PERSON> $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force

$targetDate = Get-Date -Hour 3 -Minute 0 -Second 0
$targetDate = $targetDate.AddDays(1)
try
{
	while($true)
	{
		$now = Get-Date
		if ($now -le $targetDate)
		{
			Write-Lg "Current date: $now"
			Write-Lg "Target date $targetDate not yet reached!"
			Start-Sleep -Duration (New-TimeSpan -Hours 1)
			continue
		}

		& .\Scripts\KillAllExcelApps.ps1 -prmSilent
		$years = @("2025","2026")
		$ops = @("Update")
		$updates = @("ERP", "MASTER", "EVAL", "LEADS")
		& .\Scripts\BatchOperation.ps1 -prmSilent -prmYears $years -prmOperations $ops -prmUpdates $updates
		$targetDate = $targetDate.AddDays(1)
	}
}
catch
{
	Write-Lg "An erroy occured!"
}
