param (
	[Parameter()]
    [switch]$prmStartupRun
)

$InformationPreference = 'Continue'
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force

function Invoke-BatchOperation
{
		& .\Scripts\KillAllExcelApps.ps1 -prmSilent
		$years = @("2025","2026")
		$ops = @("3 Update")
		$updates = @("ERP", "MASTER", "EVAL", "LEADS")
		& .\Scripts\BatchOperation.ps1 -prmSilent -prmYears $years -prmOperations $ops -prmUpdates $updates
}

if ($prmStartupRun)
{
	Invoke-BatchOperation
}

$targetDate = Get-Date -Hour 3 -Minute 0 -Second 0
$targetDate = $targetDate.AddDays(1)

try
{
	while($true)
	{

		$now = Get-Date
		if ($now -le $targetDate)
		{
			Write-Lg "Current date: $now"
			Write-Lg "Target date $targetDate not yet reached!"
			Start-Sleep -Duration (New-TimeSpan -Hours 1)
			continue
		}

		Write-Lg "Target date $targetDate reached!"
		Invoke-BatchOperation
		$targetDate = $targetDate.AddDays(1)
	}
}
catch
{
	Write-Lg "An error occured!"
}
