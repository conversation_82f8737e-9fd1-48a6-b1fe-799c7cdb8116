### Parameters
#
param (
	[Parameter()]
    [switch]$prmSilent,
    $prmUpdates
)

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$GlobalScriptParameters = Get-SetupParameters
$SharePointTeamViewPath = $GlobalScriptParameters.SharePointMasterTeamView
$SharePointRFCsPath = $GlobalScriptParameters.SharePointRFCsPath
$SharePointRosterViewPath = $GlobalScriptParameters.SharePointRosterView
$MasterVsBudgetFilename = $GlobalScriptParameters.MasterVsBudgetFilename
$SharePointMasterFolder = $GlobalScriptParameters.SharePointMasterFolder
$MasterVsBudgetPath = $SharePointMasterFolder + $MasterVsBudgetFilename
$SharePointWorkloadPath = $GlobalScriptParameters.SharePointWorkload
$SharePointDirectCostPath = $GlobalScriptParameters.SharePointDirectCost
# Filename of Master file (should be in script folder)
$MasterFilename = $GlobalScriptParameters.MasterFilename
$MasterSharepointPath = $SharePointMasterFolder + $MasterFilename


# Start Excel
$ExcelHndl = Start-Excel

function Update-ViewFile
{
	param(
		[Parameter(Mandatory=$true)]
		[string] $FilePath
	)

	$StartTime = Start-Timer

	$WorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $FilePath -isSharepoint $true
	$Workbook = $WorkbookHndl["workbook"]

	Update-AllQueries -WorkbookPrm $Workbook
	Wait-OnAllQueries -WorkbookPrm $Workbook
	Write-Lg "Update table ALL DONE"

	Close-Workbook -workbookHndl $WorkbookHndl
	Write-Lg "Workbook closed"

	Stop-Timer -StartTime $StartTime -OperationName $FilePath
}

function Get-ProjectLeadFiles
{
	# Query the names of all project leads and rfc managers from master file
	$RFCLeadsInfo = Get-RFCLeadsInfo -ExcelHndl $ExcelHndl -MasterFilepath $MasterSharepointPath

	$filenames = @()
	foreach ($leadFilename in $RFCLeadsInfo.Filenames)
	{
		$filenames += $SharePointRFCsPath + "$($leadFilename)"
	}
	return $filenames
}

try{
	# If no update modes are passed, we update everything
	# If update modes are passed, they will be used
	# If no update modes are passed and this is not a silent call, ask again
	$prmUpdatesAll = @("ERP", "MASTER", "EVAL", "LEADS")
	$SelectedUpdates = $prmUpdatesAll
	if($null -ne $prmUpdates){
		$SelectedUpdates = $prmUpdates
	}
	elseif(-not $prmSilent.IsPresent){
		$SelectedUpdates = Select-ByGUIWithOptions  -CandidateList $prmUpdatesAll -OptionsPrm @{"SelectionMode"="MultiExtended"}
	}

	if ("ERP" -in $SelectedUpdates)
	{
		Update-ViewFile -FilePath $SharePointWorkloadPath
		Update-ViewFile -FilePath $SharePointDirectCostPath
	}
	if ("MASTER" -in $SelectedUpdates)
	{
		Update-ViewFile -FilePath $MasterSharePointPath
	}
	if ("EVAL" -in $SelectedUpdates)
	{
		Update-ViewFile -FilePath $MasterVsBudgetPath
		Update-ViewFile -FilePath $SharePointTeamViewPath
		Update-ViewFile -FilePath $SharePointRosterViewPath
	}
	if ("LEADS" -in $SelectedUpdates)
	{
		foreach($filename in Get-ProjectLeadFiles)
		{
			Update-ViewFile -FilePath $filename
		}
	}
}
catch{
	Write-Lg "An error occurred: $_" -Level "ERROR"
	Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
	Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
}
finally{
	Stop-Excel -excelHndl $ExcelHndl
}

if (-not $prmSilent.IsPresent)
{
	Pause
}
