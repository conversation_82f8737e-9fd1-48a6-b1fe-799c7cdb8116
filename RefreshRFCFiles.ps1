# Import custom modules
Import-Module -Name (Join-<PERSON> $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force


try{
	$setupParameters = Get-SetupParameters
	$setupSharePointPath = $setupParameters["SharePointSetupFilePath"]
	$RFCFilesPath = Resolve-Path -Path ($setupParameters.RelativeRFCFilesPath)
	# Start Excel
	$excelHndl = Start-Excel

	# Open all files in the local folder to ensure synchronization. This script will often be called
	# after replacing files locally
	Write-Lg "Open and save all local RFC files..."
	$RFCFiles = Get-ChildItem -Path $RFCFilesPath -File
	foreach ($file in $RFCFiles) {
		$RFCWorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $file.FullName -isSharepoint $true
		Close-Workbook -workbookHndl $RFCWorkbookHndl
	}
}
catch{
	Write-Lg "An error occurred: $_" -Level "ERROR"
	Write-Lg "File: $($_.InvocationInfo.ScriptName)" -Level "ERROR"
	Write-Lg "Line: $($_.InvocationInfo.ScriptLineNumber)" -Level "ERROR"
}
finally	{
	if ($excelHndl -ne $null){
		Stop-Excel -excelHndl $excelHndl
	}
	Pause
}
